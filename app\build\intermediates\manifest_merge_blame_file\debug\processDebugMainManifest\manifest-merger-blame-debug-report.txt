1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.uhabits_99"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
13-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
13-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
14
15    <permission
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.uhabits_99.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.uhabits_99.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:5:5-26:19
22        android:name="com.example.habits9.HabitsApplication"
22-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:6:9-61
23        android:allowBackup="true"
23-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:7:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:8:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:9:9-54
29        android:icon="@mipmap/ic_launcher"
29-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:10:9-43
30        android:label="@string/app_name"
30-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:11:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:12:9-54
32        android:supportsRtl="true"
32-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:13:9-35
33        android:theme="@style/Theme.UHabits_99" >
33-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:14:9-48
34        <activity
34-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:15:9-25:20
35            android:name="com.example.uhabits_99.MainActivity"
35-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:16:13-41
36            android:exported="true"
36-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:17:13-36
37            android:label="@string/app_name"
37-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:18:13-45
38            android:theme="@style/Theme.UHabits_99" >
38-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:19:13-52
39            <intent-filter>
39-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:20:13-24:29
40                <action android:name="android.intent.action.MAIN" />
40-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:21:17-69
40-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:21:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:23:17-77
42-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:23:27-74
43            </intent-filter>
44        </activity>
45
46        <service
46-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:8:9-14:19
47            android:name="com.google.firebase.components.ComponentDiscoveryService"
47-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:9:13-84
48            android:directBootAware="true"
48-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
49            android:exported="false" >
49-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:10:13-37
50            <meta-data
50-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:11:13-13:85
51                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
51-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:12:17-119
52                android:value="com.google.firebase.components.ComponentRegistrar" />
52-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:13:17-82
53            <meta-data
53-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
54                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
54-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
55                android:value="com.google.firebase.components.ComponentRegistrar" />
55-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
56            <meta-data
56-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:12:13-14:85
57                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
57-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:13:17-129
58                android:value="com.google.firebase.components.ComponentRegistrar" />
58-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:14:17-82
59            <meta-data
59-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
60                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
60-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
61                android:value="com.google.firebase.components.ComponentRegistrar" />
61-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
62            <meta-data
62-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
63                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
63-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
64                android:value="com.google.firebase.components.ComponentRegistrar" />
64-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
65            <meta-data
65-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
66                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
66-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
67                android:value="com.google.firebase.components.ComponentRegistrar" />
67-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
68            <meta-data
68-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
69                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
69-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
70                android:value="com.google.firebase.components.ComponentRegistrar" />
70-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
71        </service>
72
73        <activity
73-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
74            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
74-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
75            android:excludeFromRecents="true"
75-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
76            android:exported="true"
76-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
77            android:launchMode="singleTask"
77-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
78            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
78-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
79            <intent-filter>
79-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
80                <action android:name="android.intent.action.VIEW" />
80-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
80-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
81
82                <category android:name="android.intent.category.DEFAULT" />
82-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
82-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
83                <category android:name="android.intent.category.BROWSABLE" />
83-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
83-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
84
85                <data
85-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
86                    android:host="firebase.auth"
86-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
87                    android:path="/"
87-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
88                    android:scheme="genericidp" />
88-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
89            </intent-filter>
90        </activity>
91        <activity
91-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
92            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
92-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
93            android:excludeFromRecents="true"
93-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
94            android:exported="true"
94-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
95            android:launchMode="singleTask"
95-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
96            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
96-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
97            <intent-filter>
97-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
98                <action android:name="android.intent.action.VIEW" />
98-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
98-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
99
100                <category android:name="android.intent.category.DEFAULT" />
100-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
100-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
101                <category android:name="android.intent.category.BROWSABLE" />
101-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
101-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
102
103                <data
103-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
104                    android:host="firebase.auth"
104-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
105                    android:path="/"
105-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
106                    android:scheme="recaptcha" />
106-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
107            </intent-filter>
108        </activity>
109
110        <service
110-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
111            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
111-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
112            android:enabled="true"
112-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
113            android:exported="false" >
113-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
114            <meta-data
114-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
115                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
115-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
116                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
116-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
117        </service>
118
119        <activity
119-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
120            android:name="androidx.credentials.playservices.HiddenActivity"
120-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
121            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
121-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
122            android:enabled="true"
122-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
123            android:exported="false"
123-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
124            android:fitsSystemWindows="true"
124-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
125            android:theme="@style/Theme.Hidden" >
125-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
126        </activity>
127        <activity
127-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
128            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
128-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
129            android:excludeFromRecents="true"
129-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
130            android:exported="false"
130-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
131            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
131-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
132        <!--
133            Service handling Google Sign-In user revocation. For apps that do not integrate with
134            Google Sign-In, this service will never be started.
135        -->
136        <service
136-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
137            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
137-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
138            android:exported="true"
138-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
139            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
139-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
140            android:visibleToInstantApps="true" />
140-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
141
142        <activity
142-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
143            android:name="com.google.android.gms.common.api.GoogleApiActivity"
143-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
144            android:exported="false"
144-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
145            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
145-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
146
147        <provider
147-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
148            android:name="com.google.firebase.provider.FirebaseInitProvider"
148-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
149            android:authorities="com.example.uhabits_99.firebaseinitprovider"
149-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
150            android:directBootAware="true"
150-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
151            android:exported="false"
151-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
152            android:initOrder="100" />
152-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
153
154        <activity
154-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\15984bfa3c816c6e34dc7fd6da429334\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
155            android:name="androidx.compose.ui.tooling.PreviewActivity"
155-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\15984bfa3c816c6e34dc7fd6da429334\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
156            android:exported="true" />
156-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\15984bfa3c816c6e34dc7fd6da429334\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
157        <activity
157-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d77bbb92f3326cfe733c63df1e46ad56\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
158            android:name="androidx.activity.ComponentActivity"
158-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d77bbb92f3326cfe733c63df1e46ad56\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
159            android:exported="true" />
159-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d77bbb92f3326cfe733c63df1e46ad56\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
160
161        <provider
161-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
162            android:name="androidx.startup.InitializationProvider"
162-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
163            android:authorities="com.example.uhabits_99.androidx-startup"
163-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
164            android:exported="false" >
164-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
165            <meta-data
165-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
166                android:name="androidx.emoji2.text.EmojiCompatInitializer"
166-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
167                android:value="androidx.startup" />
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
168            <meta-data
168-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
169                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
169-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
170                android:value="androidx.startup" />
170-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
171            <meta-data
171-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
172                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
172-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
173                android:value="androidx.startup" />
173-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
174        </provider>
175
176        <meta-data
176-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
177            android:name="com.google.android.gms.version"
177-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
178            android:value="@integer/google_play_services_version" />
178-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
179
180        <receiver
180-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
181            android:name="androidx.profileinstaller.ProfileInstallReceiver"
181-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
182            android:directBootAware="false"
182-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
183            android:enabled="true"
183-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
184            android:exported="true"
184-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
185            android:permission="android.permission.DUMP" >
185-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
187                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
187-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
187-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
188            </intent-filter>
189            <intent-filter>
189-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
190                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
191            </intent-filter>
192            <intent-filter>
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
193                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
194            </intent-filter>
195            <intent-filter>
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
196                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
197            </intent-filter>
198        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
199        <activity
199-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
200            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
200-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
201            android:exported="false"
201-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
202            android:stateNotNeeded="true"
202-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
203            android:theme="@style/Theme.PlayCore.Transparent" />
203-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
204    </application>
205
206</manifest>
