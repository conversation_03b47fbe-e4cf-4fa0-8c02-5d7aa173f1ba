# Prompt for Developer: Fix Incorrect Item Selection in Drag-and-Drop Sort

### **Objective**

To fix a critical bug in the "Manage Sections" screen where long-pressing a card to initiate a drag-and-drop sort selects the wrong item. The selection must be accurate and always correspond to the card the user intended to move.

### **Context & Root Cause**

While the drag-and-drop functionality on the "Manage Sections" screen is partially working, the item selection is unreliable. As shown in the reference image `47.jpg`, when the user long-presses a specific section card (e.g., "sec3"), a completely different card (e.g., "sec1") is often lifted for dragging. This behavior is inconsistent and makes the feature unusable.

**Suspected Root Cause:** The issue lies in the UI layer's gesture handling logic. The algorithm that maps the `(x, y)` coordinates of a long-press touch event to a specific item's index in the list is incorrect. It is likely failing to properly account for item heights, spacing, or the list's scroll position, resulting in an inaccurate index calculation. This is a UI-level bug, not a data or ViewModel issue.

---

### **Part 1: Detailed Implementation Plan**

1.  **Isolate the Gesture Detection Logic:**
    * Navigate to the composable file responsible for the "Manage Sections" screen.
    * Identify the code that implements the gesture detection for the list of draggable section cards. Focus on the `onDragStart` or the long-press handler that initiates the drag.

2.  **Debug the Index Calculation:**
    * The primary task is to find and fix the error in the index calculation.
    * Inside the gesture handler, add logging to trace the logic:
        * Log the raw `y` coordinate of the touch event.
        * Log the list's current scroll offset.
        * Log the final calculated item `index` that the code believes is being pressed.
        * Log the name or ID of the section at that `index` to confirm if it matches the user's touch target.
    * This will expose the discrepancy between the intended item and the one being selected.

3.  **Correct the Mapping Logic:**
    * Rewrite the index calculation to be accurate. The logic must correctly factor in:
        * The rendered height of each card in the list.
        * Any vertical spacing or dividers between the cards.
        * The current scroll offset of the `LazyColumn` or list container.
    * The calculation `(touch_y + scroll_offset) / item_height_with_spacing` is a common pattern, but it must be adapted precisely to the layout's structure.

---

### **Part 2: Verification Steps**

To confirm the fix, perform the following tests rigorously:

1.  **Setup:** Navigate to the "Manage Sections" screen with a list of at least 4-5 sections to ensure scrolling is possible.
2.  **Test First Item:** Long-press the **very first** section card in the list. Verify that **only this card** is selected and lifts for dragging.
3.  **Test Last Item:** Scroll to the bottom. Long-press the **very last** section card. Verify that **only this card** is selected.
4.  **Test Middle Item:** Long-press a card in the **middle** of the list. Verify that the correct card is selected.
5.  **Test After Scrolling:** Scroll the list up and down a few times and stop at a random position. Long-press a card that is fully visible. Confirm the correct card is always selected, proving that the scroll offset is handled correctly.
6.  **Complete a Drag:** After confirming the selection is correct, complete a drag-and-drop action to ensure the reordering still works as expected.

---

### **⚙️ Mandatory Development Guidelines**

**This is a mandatory section. Adherence to these guidelines is required for all development work on this project.**

1.  **Refer to the GitHub Project First:** Before writing any code, you must consult the GitHub link I have shared with you. Spend time exploring and understanding the current project structure, architecture, and existing implementations.
2.  **Study the Reference Project (`uhabits-dev`):** Inside the project, you will find the `uhabits-dev` folder. This is our reference project and blueprint. Review how similar features have been built there to ensure consistency. **This step is not optional.**
3.  **Consult the Style Guide (`style.md`):** All UI/UX decisions, including colors, fonts, spacing, and component design, must strictly adhere to the rules defined in the `style.md` file located in the root folder. It is the single source of truth for all styling.
4.  **Maintain a Clean Codebase:** After implementation, you must remove any temporary files, test code, or unused components created during development. The codebase must remain organized.
5.  **Remove Unused Old Implementations:** As part of your final review, identify and delete any obsolete code that is no longer in use.
6.  **Pause If There Is Any Confusion:** If any requirement is unclear, **stop work immediately**. Do not make assumptions. Seek clarification from me to prevent rework.