
> Task :app:compileDebugKotlin
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.kt:152:51 @Composable invocations can only happen from the context of a @Composable function
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.kt:155:54 @Composable invocations can only happen from the context of a @Composable function
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.kt:162:51 None of the following candidates is applicable:
val ColorSchemeKeyTokens.value: Color
val ShapeKeyTokens.value: Shape
val TypographyKeyTokens.value: TextStyle
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.kt:178:61 None of the following candidates is applicable:
val ColorSchemeKeyTokens.value: Color
val ShapeKeyTokens.value: Shape
val TypographyKeyTokens.value: TextStyle
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.kt:179:65 None of the following candidates is applicable:
val ColorSchemeKeyTokens.value: Color
val ShapeKeyTokens.value: Shape
val TypographyKeyTokens.value: TextStyle
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.kt:181:37 Unresolved reference 'not' for operator '!'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.kt:181:57 None of the following candidates is applicable:
val ColorSchemeKeyTokens.value: Color
val ShapeKeyTokens.value: Shape
val TypographyKeyTokens.value: TextStyle
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.kt:186:37 Unresolved reference 'not' for operator '!'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.kt:186:57 None of the following candidates is applicable:
val ColorSchemeKeyTokens.value: Color
val ShapeKeyTokens.value: Shape
val TypographyKeyTokens.value: TextStyle

> Task :app:compileDebugKotlin FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 30s
30 actionable tasks: 2 executed, 4 from cache, 24 up-to-date
